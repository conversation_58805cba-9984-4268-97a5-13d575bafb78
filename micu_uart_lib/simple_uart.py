# simple_uart.py - Simple UART management module

from maix import uart, time
from .config import config
from .utils import micu_printf, safe_decode, Timer, set_global_uart, extract_and_apply_data
import threading

class SimpleUART:
    """Simple UART manager with direct buffer refresh mode and data extraction"""
    
    def __init__(self):
        self.serial = None
        self.rx_buf = ""  # Receive buffer for text data
        self.binary_buf = b''  # Receive buffer for binary data
        self.is_initialized = False
        self.refresh_timer = Timer(config.refresh_interval)
        self.auto_refresh = True  # Auto refresh mode
        self.auto_extract = False  # Auto extract key=value pairs

        # Binary packet callbacks
        self.binary_packet_callbacks = []  # List of callback functions for binary packets

        # Thread safety
        self._buffer_lock = threading.Lock()  # 缓冲区锁
        self._extract_lock = threading.Lock()  # 数据提取锁
        self._binary_lock = threading.Lock()  # 二进制缓冲区锁
    
    def init(self, device=None, baudrate=None, set_as_global=True):
        """Initialize UART
        
        Args:
            device (str, optional): UART device path, None to use config default
            baudrate (int, optional): Baud rate, None to use config default
            set_as_global (bool): Whether to set as global UART instance for micu_printf
        """
        try:
            # Use provided parameters or config defaults
            uart_device = device if device is not None else config.uart_device
            uart_baudrate = baudrate if baudrate is not None else config.uart_baudrate
            
            self.serial = uart.UART(uart_device, uart_baudrate)
            self.serial.set_received_callback(self._on_received)
            
            self.is_initialized = True
            
            # Set as global UART instance for micu_printf
            if set_as_global:
                set_global_uart(self)
            
            print(f"UART initialized successfully - {uart_device}:{uart_baudrate}")
            if set_as_global:
                print("Set as global UART instance, micu_printf available")
            self._show_frame_config()
            
            return True
            
        except Exception as e:
            print(f"UART initialization failed: {str(e)}")
            self.is_initialized = False
            return False
    
    def set_frame(self, header="$$", tail="##", enabled=True):
        """Set global frame header and tail (used for all send/receive)
        
        Args:
            header (str): Frame header
            tail (str): Frame tail
            enabled (bool): Whether to enable frame detection
        """
        config.set_frame_format(header, tail, enabled)
        print(f"Frame format: {header}...{tail} ({'enabled' if enabled else 'disabled'})")
    
    def set_auto_refresh(self, enabled=True):
        """Set auto refresh mode
        
        Args:
            enabled (bool): Whether to enable auto refresh
        """
        self.auto_refresh = enabled
        print(f"Auto refresh: {'enabled' if enabled else 'disabled'}")
    
    def set_auto_extract(self, enabled=True):
        """Set auto data extraction mode
        
        Args:
            enabled (bool): Whether to enable auto extraction of key=value pairs
        """
        self.auto_extract = enabled
        print(f"Auto data extraction: {'enabled' if enabled else 'disabled'}")
    
    def _show_frame_config(self):
        """Show current frame format configuration"""
        frame_config = config.get_frame_config()
        if frame_config["enabled"]:
            print(f"Frame format: {frame_config['header']}...{frame_config['tail']}")
        else:
            print("Frame format: disabled (line-based processing)")
    
    def _on_received(self, serial_obj, data: bytes):
        """UART data receive callback - Thread Safe (参考串口监听器逻辑)"""
        try:
            # 直接处理原始bytes数据，参考串口监听器的方法
            with self._binary_lock:
                self.binary_buf += data  # 累积所有原始数据到二进制缓冲区

            print(f"\n📨 收到 {len(data)} 字节数据")
            print(f"原始数据: {data.hex().upper()}")

            # 尝试解析为文本（参考串口监听器逻辑）
            self._try_parse_as_text(data)

            # 检查特殊二进制指令
            self._check_binary_commands(data)

            # 检查并处理三角形数据包
            self._process_triangle_packets()

            # 清理已处理的数据包
            self._cleanup_processed_packets()

        except Exception as e:
            print(f"Receive error: {str(e)}")

    def _try_parse_as_text(self, data: bytes):
        """尝试解析为文本（参考串口监听器逻辑）"""
        try:
            text = data.decode('utf-8', errors='ignore').strip()
            if text and all(32 <= ord(c) <= 126 or c in '\r\n\t' for c in text):
                print(f"📝 文本内容: '{text}'")

                # 处理文本帧格式
                frame_config = config.get_frame_config()
                if frame_config["enabled"]:
                    clean_data = self._extract_frame_content(text, frame_config)
                    if clean_data:
                        with self._buffer_lock:
                            self._update_buffer(clean_data)

                        # 自动数据提取
                        if self.auto_extract and clean_data.strip():
                            with self._extract_lock:
                                extract_and_apply_data(clean_data.strip())
                else:
                    # 无帧模式，直接处理
                    with self._buffer_lock:
                        self._update_buffer(text)

                # 检查特殊指令
                if text == "?":
                    print("❓ 检测到状态查询!")

                return True
            else:
                print("🔢 二进制数据")
                return False
        except:
            print("🔢 二进制数据")
            return False

    def _check_binary_commands(self, data: bytes):
        """检查特殊二进制指令（参考串口监听器逻辑）"""
        if b'\xFF\x00' in data:
            print("🚀 检测到启动指令 FF 00!")
        if b'\xFF\xFF' in data:
            print("🛑 检测到停止指令 FF FF!")

    def _process_triangle_packets(self):
        """处理三角形数据包（参考串口监听器逻辑）"""
        with self._binary_lock:
            packets = self._find_triangle_packets_in_buffer()
            for packet in packets:
                self._analyze_triangle_packet(packet)
                # 调用注册的回调函数
                for callback in self.binary_packet_callbacks:
                    try:
                        callback(packet)
                    except Exception as e:
                        print(f"Binary packet callback error in {callback.__name__}: {e}")

    def _find_triangle_packets_in_buffer(self):
        """在缓冲区中查找三角形数据包（参考串口监听器逻辑）"""
        packets = []
        i = 0

        while i < len(self.binary_buf) - 6:  # 至少需要7字节 (0x78 + 5字节数据 + 0xFC)
            if self.binary_buf[i] == 0x78:  # 包头
                # 查找对应的包尾
                for j in range(i + 6, min(i + 32, len(self.binary_buf))):  # 最大搜索范围32字节
                    if self.binary_buf[j] == 0xFC:  # 包尾
                        packet = self.binary_buf[i:j+1]
                        packets.append(packet)
                        # 从缓冲区移除已处理的数据包
                        self.binary_buf = self.binary_buf[:i] + self.binary_buf[j+1:]
                        break
                else:
                    i += 1
            else:
                i += 1

        return packets

    def _analyze_triangle_packet(self, packet: bytes):
        """分析三角形数据包（参考串口监听器逻辑）"""
        print(f"\n🎯 三角形数据包")
        print(f"完整数据包: {packet.hex().upper()}")

        if len(packet) >= 7:  # 最小数据包长度
            try:
                packet_id = packet[1]
                # 解析坐标（根据实际数据包格式）
                if len(packet) == 7:  # 7字节格式: 0x78 + ID + x_high + x_low + y_high + y_low + 0xFC
                    x_coord = (packet[2] << 8) | packet[3]
                    y_coord = (packet[4] << 8) | packet[5]

                    # 处理有符号坐标
                    if x_coord > 32767:
                        x_coord = x_coord - 65536
                    if y_coord > 32767:
                        y_coord = y_coord - 65536

                    print(f"📐 ID: {packet_id}, 坐标: ({x_coord:+d}, {y_coord:+d})")

                elif len(packet) == 10:  # 10字节格式: 0x78 + 目标坐标 + 激光坐标 + 0xFC
                    target_x = (packet[1] << 8) | packet[2]
                    target_y = (packet[3] << 8) | packet[4]
                    laser_x = (packet[5] << 8) | packet[6]
                    laser_y = (packet[7] << 8) | packet[8]

                    print(f"🎯 目标点: ({target_x:3d}, {target_y:3d})")
                    print(f"🔴 激光点: ({laser_x:3d}, {laser_y:3d})")

                    # 计算距离和方向
                    dx = target_x - laser_x
                    dy = target_y - laser_y
                    distance = (dx*dx + dy*dy)**0.5

                    print(f"📏 距离: {distance:.1f} 像素")
                    print(f"📐 偏移: X={dx:+d}, Y={dy:+d}")

                    # 判断激光状态
                    if distance < 15:
                        print("✅ 激光接近目标点")
                    elif distance < 50:
                        print("🟡 激光正在接近目标")
                    else:
                        print("🔴 激光距离目标较远")

            except Exception as e:
                print(f"数据包解析错误: {e}")

    def _cleanup_processed_packets(self):
        """清理已处理的数据包，保留未完整的数据（参考串口监听器逻辑）"""
        with self._binary_lock:
            # 如果缓冲区太大，清空它
            if len(self.binary_buf) > config.max_buffer_size:
                print("Binary buffer overflow, clearing...")
                self.binary_buf = b''

    def _extract_frame_content(self, data, frame_config):
        """Extract content from frame"""
        header = frame_config["header"]
        tail = frame_config["tail"]
        
        # Check if data contains complete frame
        if header in data and tail in data:
            # Find complete frame
            header_pos = data.find(header)
            tail_pos = data.find(tail, header_pos + len(header))
            
            if header_pos != -1 and tail_pos != -1:
                # Extract content between header and tail
                frame_content = data[header_pos + len(header):tail_pos]
                print(f"Valid frame received - content: {frame_content.strip()}")
                return frame_content.strip()
            else:
                print(f"Invalid frame format - ignoring data: {data}")
        else:
            print(f"No complete frame found - ignoring data: {data}")
        
        return None
    
    def _update_buffer(self, data):
        """Update receive buffer with new data - Called with lock held"""
        if self.auto_refresh:
            # Direct refresh mode: replace buffer with new data
            self.rx_buf = data
            print(f"Buffer refreshed - new data: {data[:50] + '...' if len(data) > 50 else data}")
        else:
            # Accumulate mode: add data to buffer
            if len(self.rx_buf) + len(data) > config.max_buffer_size:
                print("Buffer full, replacing with new data")
                self.rx_buf = data
            else:
                self.rx_buf += data
                print(f"Data added to buffer: {data}")
    
    def send(self, data):
        """Send data (using global frame header/tail settings) - Enhanced with retry
        
        Args:
            data (str): Data to send
        """
        if not self.is_initialized:
            print("UART not initialized")
            return False
        
        try:
            # Ensure input data is properly formatted
            if isinstance(data, bytes):
                data = data.decode('utf-8', errors='replace')
            else:
                data = str(data)
            
            frame_config = config.get_frame_config()
            
            if frame_config["enabled"]:
                # Use global configured frame header/tail
                frame_data = frame_config["header"] + data + frame_config["tail"]
            else:
                frame_data = data
            
            # Add line ending
            final_data = frame_data + "\r\n"
            
            # Try to send with retry mechanism
            max_retries = 3
            retry_delay = 10  # ms
            
            for attempt in range(max_retries):
                try:
                    # Send data
                    result = self.serial.write_str(final_data)
                    
                    # Check if write was successful
                    if result is not None and result >= 0:
                        return True
                    else:
                        if attempt < max_retries - 1:
                            print(f"Send attempt {attempt + 1} failed, retrying...")
                            time.sleep_ms(retry_delay)
                        else:
                            print(f"Send failed after {max_retries} attempts")
                            return False
                            
                except Exception as e:
                    if attempt < max_retries - 1:
                        print(f"Send attempt {attempt + 1} error: {e}, retrying...")
                        time.sleep_ms(retry_delay)
                    else:
                        print(f"Send failed after {max_retries} attempts: {e}")
                        return False
            
            return False
            
        except Exception as e:
            print(f"Send failed: {str(e)}")
            return False
    
    def receive(self):
        """Receive one complete data frame (using global frame header/tail settings) - Thread Safe
        
        Returns:
            str or None: Extracted data content, None if no complete frame
        """
        with self._buffer_lock:
            if not self.rx_buf:
                return None
                
            frame_config = config.get_frame_config()
            
            if not frame_config["enabled"]:
                # If frame detection disabled, return buffer data directly and clear
                data = self.rx_buf.strip()
                self.rx_buf = ""  # Clear buffer immediately
                return data if data else None
            
            # Use global configured frame header/tail
            header = frame_config["header"]
            tail = frame_config["tail"]
            
            return self._extract_frame_with_delimiters(header, tail)
    
    def receive_all(self):
        """Receive all available complete frames (using global frame header/tail settings) - Thread Safe
        
        Returns:
            list: List of all complete frame data
        """
        frames = []
        
        with self._buffer_lock:
            if not self.rx_buf:
                return frames
                
            frame_config = config.get_frame_config()
            
            if not frame_config["enabled"]:
                # If frame detection disabled, return entire buffer as one item
                if self.rx_buf.strip():
                    frames.append(self.rx_buf.strip())
                self.rx_buf = ""  # Clear buffer
                return frames
        
        # Extract all complete frames (release lock during extraction)
        while True:
            frame_data = self.receive()
            if frame_data is None:
                break
            frames.append(frame_data)
        
        return frames
    
    def extract_data_from_buffer(self):
        """Extract key:value pairs from current buffer - Thread Safe
        
        Returns:
            dict: Dictionary of applied variables
        """
        with self._buffer_lock:
            if not self.rx_buf:
                print("Buffer is empty")
                return {}
            
            buffer_copy = self.rx_buf.strip()
        
        # Extract data outside of lock
        with self._extract_lock:
            return extract_and_apply_data(buffer_copy)
    
    def _extract_frame_with_delimiters(self, header, tail):
        """Extract data with specified frame header/tail - Called with lock held"""
        # Find header
        header_pos = self.rx_buf.find(header)
        if header_pos == -1:
            return None
        
        # Find tail starting from header position
        tail_pos = self.rx_buf.find(tail, header_pos + len(header))
        if tail_pos == -1:
            return None  # No complete frame found
        
        # Extract data from frame (remove header/tail)
        data = self.rx_buf[header_pos + len(header):tail_pos]
        
        # Clear entire buffer directly (refresh mode)
        self.rx_buf = ""
        
        return data.strip()
    
    def get_buffer(self):
        """Get raw buffer content - Thread Safe"""
        with self._buffer_lock:
            return self.rx_buf
    
    def clear_buffer(self):
        """Clear receive buffer - Thread Safe"""
        with self._buffer_lock:
            self.rx_buf = ""
        print("Buffer cleared")
    
    def flush_buffer(self):
        """Force flush buffer - get and clear all data - Thread Safe"""
        with self._buffer_lock:
            data = self.rx_buf
            self.rx_buf = ""
        print("Buffer force flushed")
        return data
    
    def has_data(self):
        """Check if there is data in buffer - Thread Safe"""
        with self._buffer_lock:
            return len(self.rx_buf) > 0
    
    def buffer_size(self):
        """Get current buffer size - Thread Safe"""
        with self._buffer_lock:
            return len(self.rx_buf)
    
    def refresh(self):
        """Timer refresh processing - Thread Safe"""
        if self.refresh_timer.is_timeout():
            if self.auto_refresh:
                with self._buffer_lock:
                    if self.rx_buf:
                        # In auto refresh mode, periodically clean old data
                        print("Timer refresh: clearing buffer")
                        self.rx_buf = ""
    
    def close(self):
        """Close UART"""
        if self.serial:
            try:
                self.serial.close()
                print("UART closed")
            except:
                pass
        
        self.is_initialized = False
        self.serial = None

    def receive_binary_packets(self):
        """Receive all available binary packets - Thread Safe (参考串口监听器逻辑)

        Returns:
            list: List of binary packets (bytes objects)
        """
        packets = []

        with self._binary_lock:
            if not self.binary_buf:
                return packets

            # 使用串口监听器的逻辑查找数据包
            packets = self._find_triangle_packets_in_buffer()

        return packets

    def get_binary_buffer_info(self):
        """Get binary buffer status information

        Returns:
            dict: Buffer information including size and content preview
        """
        with self._binary_lock:
            return {
                "size": len(self.binary_buf),
                "content_hex": self.binary_buf.hex().upper() if self.binary_buf else "",
                "preview": self.binary_buf[:16].hex().upper() if len(self.binary_buf) > 16 else self.binary_buf.hex().upper()
            }