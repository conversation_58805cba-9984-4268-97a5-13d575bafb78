# simple_uart.py - Simple UART management module

from maix import uart, time
from .config import config
from .utils import micu_printf, safe_decode, Timer, set_global_uart, extract_and_apply_data
import threading

class SimpleUART:
    """Simple UART manager with direct buffer refresh mode and data extraction"""
    
    def __init__(self):
        self.serial = None
        self.rx_buf = ""  # Receive buffer for text data
        self.binary_buf = b''  # Receive buffer for binary data
        self.is_initialized = False
        self.refresh_timer = Timer(config.refresh_interval)
        self.auto_refresh = True  # Auto refresh mode
        self.auto_extract = False  # Auto extract key=value pairs

        # Binary packet callbacks
        self.binary_packet_callbacks = []  # List of callback functions for binary packets

        # Thread safety
        self._buffer_lock = threading.Lock()  # 缓冲区锁
        self._extract_lock = threading.Lock()  # 数据提取锁
        self._binary_lock = threading.Lock()  # 二进制缓冲区锁
    
    def init(self, device=None, baudrate=None, set_as_global=True):
        """Initialize UART
        
        Args:
            device (str, optional): UART device path, None to use config default
            baudrate (int, optional): Baud rate, None to use config default
            set_as_global (bool): Whether to set as global UART instance for micu_printf
        """
        try:
            # Use provided parameters or config defaults
            uart_device = device if device is not None else config.uart_device
            uart_baudrate = baudrate if baudrate is not None else config.uart_baudrate
            
            self.serial = uart.UART(uart_device, uart_baudrate)
            self.serial.set_received_callback(self._on_received)
            
            self.is_initialized = True
            
            # Set as global UART instance for micu_printf
            if set_as_global:
                set_global_uart(self)
            
            print(f"UART initialized successfully - {uart_device}:{uart_baudrate}")
            if set_as_global:
                print("Set as global UART instance, micu_printf available")
            self._show_frame_config()
            
            return True
            
        except Exception as e:
            print(f"UART initialization failed: {str(e)}")
            self.is_initialized = False
            return False
    
    def set_frame(self, header="$$", tail="##", enabled=True):
        """Set global frame header and tail (used for all send/receive)
        
        Args:
            header (str): Frame header
            tail (str): Frame tail
            enabled (bool): Whether to enable frame detection
        """
        config.set_frame_format(header, tail, enabled)
        print(f"Frame format: {header}...{tail} ({'enabled' if enabled else 'disabled'})")
    
    def set_auto_refresh(self, enabled=True):
        """Set auto refresh mode
        
        Args:
            enabled (bool): Whether to enable auto refresh
        """
        self.auto_refresh = enabled
        print(f"Auto refresh: {'enabled' if enabled else 'disabled'}")
    
    def set_auto_extract(self, enabled=True):
        """Set auto data extraction mode

        Args:
            enabled (bool): Whether to enable auto extraction of key=value pairs
        """
        self.auto_extract = enabled
        print(f"Auto data extraction: {'enabled' if enabled else 'disabled'}")

    def set_binary_mode(self, enabled=True, mode="mixed", packet_header=0x78, packet_tail=0xFC):
        """Set binary data processing mode

        Args:
            enabled (bool): Enable binary data processing
            mode (str): Processing mode - "text_only", "binary_only", "mixed"
            packet_header (int): Binary packet header byte
            packet_tail (int): Binary packet tail byte
        """
        config.set_binary_mode(enabled, mode, packet_header, packet_tail)
        print(f"Binary mode: {'enabled' if enabled else 'disabled'} (mode: {mode})")
        print(f"Binary packet format: 0x{packet_header:02X}...0x{packet_tail:02X}")

    def add_binary_packet_callback(self, callback):
        """Add callback function for binary packet processing

        Args:
            callback (function): Callback function that takes (packet_data: bytes) as parameter
        """
        if callback not in self.binary_packet_callbacks:
            self.binary_packet_callbacks.append(callback)
            print(f"Binary packet callback added: {callback.__name__}")

    def remove_binary_packet_callback(self, callback):
        """Remove binary packet callback

        Args:
            callback (function): Callback function to remove
        """
        if callback in self.binary_packet_callbacks:
            self.binary_packet_callbacks.remove(callback)
            print(f"Binary packet callback removed: {callback.__name__}")
    
    def _show_frame_config(self):
        """Show current frame format configuration"""
        frame_config = config.get_frame_config()
        if frame_config["enabled"]:
            print(f"Frame format: {frame_config['header']}...{frame_config['tail']}")
        else:
            print("Frame format: disabled (line-based processing)")
    
    def _on_received(self, serial_obj, data: bytes):
        """UART data receive callback - Thread Safe with Binary Support"""
        try:
            binary_config = config.get_binary_config()

            if binary_config["enabled"]:
                # Binary mode enabled - process based on mode
                if binary_config["mode"] == "binary_only":
                    self._process_binary_data(data)
                elif binary_config["mode"] == "text_only":
                    self._process_text_data(data)
                else:  # mixed mode
                    self._process_mixed_data(data)
            else:
                # Legacy text-only mode
                self._process_text_data(data)

        except Exception as e:
            print(f"Receive error: {str(e)}")

    def _process_text_data(self, data: bytes):
        """Process text data using original logic"""
        decoded_data = safe_decode(data)
        if decoded_data is None:
            return

        frame_config = config.get_frame_config()

        if frame_config["enabled"]:
            # Frame mode: extract complete frames
            clean_data = self._extract_frame_content(decoded_data, frame_config)
            if clean_data is None:
                return  # No valid frame found
        else:
            # No frame mode: accept all data
            clean_data = decoded_data

        # Thread-safe buffer update
        with self._buffer_lock:
            self._update_buffer(clean_data)

        # Thread-safe data extraction
        if self.auto_extract and clean_data.strip():
            with self._extract_lock:
                extract_and_apply_data(clean_data.strip())

    def _process_binary_data(self, data: bytes):
        """Process pure binary data"""
        with self._binary_lock:
            self.binary_buf += data
            # Extract and process complete binary packets
            packets = self._extract_binary_packets()
            for packet in packets:
                self._handle_binary_packet(packet)

    def _process_mixed_data(self, data: bytes):
        """Process mixed text and binary data"""
        # Try to detect if data is likely binary or text
        if self._is_likely_binary(data):
            self._process_binary_data(data)
        else:
            self._process_text_data(data)
    
    def _extract_frame_content(self, data, frame_config):
        """Extract content from frame"""
        header = frame_config["header"]
        tail = frame_config["tail"]
        
        # Check if data contains complete frame
        if header in data and tail in data:
            # Find complete frame
            header_pos = data.find(header)
            tail_pos = data.find(tail, header_pos + len(header))
            
            if header_pos != -1 and tail_pos != -1:
                # Extract content between header and tail
                frame_content = data[header_pos + len(header):tail_pos]
                print(f"Valid frame received - content: {frame_content.strip()}")
                return frame_content.strip()
            else:
                print(f"Invalid frame format - ignoring data: {data}")
        else:
            print(f"No complete frame found - ignoring data: {data}")
        
        return None

    def _is_likely_binary(self, data: bytes):
        """Detect if data is likely binary based on content analysis"""
        binary_config = config.get_binary_config()

        # Check for binary packet header
        if binary_config["packet_header"] in data:
            return True

        # Check for non-printable characters (excluding common control chars)
        try:
            decoded = data.decode('utf-8', errors='strict')
            # If successful decode and mostly printable, likely text
            printable_count = sum(1 for c in decoded if c.isprintable() or c in '\r\n\t')
            return printable_count / len(decoded) < 0.8
        except UnicodeDecodeError:
            # Failed to decode as UTF-8, likely binary
            return True

    def _extract_binary_packets(self):
        """Extract complete binary packets from buffer - Called with binary_lock held"""
        binary_config = config.get_binary_config()
        header = binary_config["packet_header"]
        tail = binary_config["packet_tail"]
        min_size = binary_config["min_packet_size"]
        max_size = binary_config["max_packet_size"]

        packets = []
        i = 0

        while i < len(self.binary_buf):
            # Look for packet header
            if self.binary_buf[i] == header:
                # Search for corresponding tail within reasonable distance
                for j in range(i + min_size - 1, min(i + max_size, len(self.binary_buf))):
                    if self.binary_buf[j] == tail:
                        # Found complete packet
                        packet = self.binary_buf[i:j+1]
                        packets.append(packet)
                        # Remove processed packet from buffer
                        self.binary_buf = self.binary_buf[:i] + self.binary_buf[j+1:]
                        break
                else:
                    # No tail found, move to next byte
                    i += 1
            else:
                i += 1

        # Clean up buffer if it gets too large
        if len(self.binary_buf) > config.max_buffer_size:
            print("Binary buffer overflow, clearing...")
            self.binary_buf = b''

        return packets

    def _handle_binary_packet(self, packet: bytes):
        """Handle a complete binary packet"""
        print(f"Binary packet received: {packet.hex().upper()} (length: {len(packet)})")

        # Call all registered callbacks
        for callback in self.binary_packet_callbacks:
            try:
                callback(packet)
            except Exception as e:
                print(f"Binary packet callback error in {callback.__name__}: {e}")
    
    def _update_buffer(self, data):
        """Update receive buffer with new data - Called with lock held"""
        if self.auto_refresh:
            # Direct refresh mode: replace buffer with new data
            self.rx_buf = data
            print(f"Buffer refreshed - new data: {data[:50] + '...' if len(data) > 50 else data}")
        else:
            # Accumulate mode: add data to buffer
            if len(self.rx_buf) + len(data) > config.max_buffer_size:
                print("Buffer full, replacing with new data")
                self.rx_buf = data
            else:
                self.rx_buf += data
                print(f"Data added to buffer: {data}")
    
    def send(self, data):
        """Send data (using global frame header/tail settings) - Enhanced with retry
        
        Args:
            data (str): Data to send
        """
        if not self.is_initialized:
            print("UART not initialized")
            return False
        
        try:
            # Ensure input data is properly formatted
            if isinstance(data, bytes):
                data = data.decode('utf-8', errors='replace')
            else:
                data = str(data)
            
            frame_config = config.get_frame_config()
            
            if frame_config["enabled"]:
                # Use global configured frame header/tail
                frame_data = frame_config["header"] + data + frame_config["tail"]
            else:
                frame_data = data
            
            # Add line ending
            final_data = frame_data + "\r\n"
            
            # Try to send with retry mechanism
            max_retries = 3
            retry_delay = 10  # ms
            
            for attempt in range(max_retries):
                try:
                    # Send data
                    result = self.serial.write_str(final_data)
                    
                    # Check if write was successful
                    if result is not None and result >= 0:
                        return True
                    else:
                        if attempt < max_retries - 1:
                            print(f"Send attempt {attempt + 1} failed, retrying...")
                            time.sleep_ms(retry_delay)
                        else:
                            print(f"Send failed after {max_retries} attempts")
                            return False
                            
                except Exception as e:
                    if attempt < max_retries - 1:
                        print(f"Send attempt {attempt + 1} error: {e}, retrying...")
                        time.sleep_ms(retry_delay)
                    else:
                        print(f"Send failed after {max_retries} attempts: {e}")
                        return False
            
            return False

        except Exception as e:
            print(f"Send failed: {str(e)}")
            return False

    def send_binary(self, data):
        """Send binary data directly without text frame processing

        Args:
            data (bytes or list): Binary data to send

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.is_initialized:
            print("UART not initialized")
            return False

        try:
            # Convert data to bytes if needed
            if isinstance(data, list):
                data = bytes(data)
            elif isinstance(data, str):
                # Convert hex string to bytes if needed
                if all(c in '0123456789ABCDEFabcdef' for c in data.replace(' ', '')):
                    data = bytes.fromhex(data.replace(' ', ''))
                else:
                    data = data.encode('utf-8')
            elif not isinstance(data, bytes):
                data = bytes(data)

            # Send binary data directly
            result = self.serial.write(data)

            if result is not None and result >= 0:
                print(f"Binary data sent: {data.hex().upper()} (length: {len(data)})")
                return True
            else:
                print("Binary send failed")
                return False

        except Exception as e:
            print(f"Binary send failed: {str(e)}")
            return False
    
    def receive(self):
        """Receive one complete data frame (using global frame header/tail settings) - Thread Safe
        
        Returns:
            str or None: Extracted data content, None if no complete frame
        """
        with self._buffer_lock:
            if not self.rx_buf:
                return None
                
            frame_config = config.get_frame_config()
            
            if not frame_config["enabled"]:
                # If frame detection disabled, return buffer data directly and clear
                data = self.rx_buf.strip()
                self.rx_buf = ""  # Clear buffer immediately
                return data if data else None
            
            # Use global configured frame header/tail
            header = frame_config["header"]
            tail = frame_config["tail"]
            
            return self._extract_frame_with_delimiters(header, tail)
    
    def receive_all(self):
        """Receive all available complete frames (using global frame header/tail settings) - Thread Safe
        
        Returns:
            list: List of all complete frame data
        """
        frames = []
        
        with self._buffer_lock:
            if not self.rx_buf:
                return frames
                
            frame_config = config.get_frame_config()
            
            if not frame_config["enabled"]:
                # If frame detection disabled, return entire buffer as one item
                if self.rx_buf.strip():
                    frames.append(self.rx_buf.strip())
                self.rx_buf = ""  # Clear buffer
                return frames
        
        # Extract all complete frames (release lock during extraction)
        while True:
            frame_data = self.receive()
            if frame_data is None:
                break
            frames.append(frame_data)
        
        return frames

    def receive_binary_packets(self):
        """Receive all available binary packets - Thread Safe

        Returns:
            list: List of binary packets (bytes objects)
        """
        packets = []

        with self._binary_lock:
            if not self.binary_buf:
                return packets

            # Extract all complete packets
            packets = self._extract_binary_packets()

        return packets

    def get_binary_buffer_info(self):
        """Get binary buffer status information

        Returns:
            dict: Buffer information including size and content preview
        """
        with self._binary_lock:
            return {
                "size": len(self.binary_buf),
                "content_hex": self.binary_buf.hex().upper() if self.binary_buf else "",
                "preview": self.binary_buf[:16].hex().upper() if len(self.binary_buf) > 16 else self.binary_buf.hex().upper()
            }
    
    def extract_data_from_buffer(self):
        """Extract key:value pairs from current buffer - Thread Safe
        
        Returns:
            dict: Dictionary of applied variables
        """
        with self._buffer_lock:
            if not self.rx_buf:
                print("Buffer is empty")
                return {}
            
            buffer_copy = self.rx_buf.strip()
        
        # Extract data outside of lock
        with self._extract_lock:
            return extract_and_apply_data(buffer_copy)
    
    def _extract_frame_with_delimiters(self, header, tail):
        """Extract data with specified frame header/tail - Called with lock held"""
        # Find header
        header_pos = self.rx_buf.find(header)
        if header_pos == -1:
            return None
        
        # Find tail starting from header position
        tail_pos = self.rx_buf.find(tail, header_pos + len(header))
        if tail_pos == -1:
            return None  # No complete frame found
        
        # Extract data from frame (remove header/tail)
        data = self.rx_buf[header_pos + len(header):tail_pos]
        
        # Clear entire buffer directly (refresh mode)
        self.rx_buf = ""
        
        return data.strip()
    
    def get_buffer(self):
        """Get raw buffer content - Thread Safe"""
        with self._buffer_lock:
            return self.rx_buf
    
    def clear_buffer(self):
        """Clear receive buffer - Thread Safe"""
        with self._buffer_lock:
            self.rx_buf = ""
        print("Buffer cleared")
    
    def flush_buffer(self):
        """Force flush buffer - get and clear all data - Thread Safe"""
        with self._buffer_lock:
            data = self.rx_buf
            self.rx_buf = ""
        print("Buffer force flushed")
        return data
    
    def has_data(self):
        """Check if there is data in buffer - Thread Safe"""
        with self._buffer_lock:
            return len(self.rx_buf) > 0
    
    def buffer_size(self):
        """Get current buffer size - Thread Safe"""
        with self._buffer_lock:
            return len(self.rx_buf)
    
    def refresh(self):
        """Timer refresh processing - Thread Safe"""
        if self.refresh_timer.is_timeout():
            if self.auto_refresh:
                with self._buffer_lock:
                    if self.rx_buf:
                        # In auto refresh mode, periodically clean old data
                        print("Timer refresh: clearing buffer")
                        self.rx_buf = ""
    
    def close(self):
        """Close UART"""
        if self.serial:
            try:
                self.serial.close()
                print("UART closed")
            except:
                pass
        
        self.is_initialized = False
        self.serial = None 