# config.py - Configuration management module

class Config:
    """System configuration management class"""

    def __init__(self):
        # UART configuration
        self.uart_device = "/dev/ttyS0"
        self.uart_baudrate = 115200

        # Buffer configuration
        self.max_buffer_size = 1024

        # Refresh mode configuration
        self.refresh_interval = 500  # ms - Timer refresh interval

        # Frame format configuration
        self.frame_config = {
            "header": "$$",      # Default frame header
            "tail": "##",        # Default frame tail
            "enabled": True      # Enable frame detection
        }

        # Binary data processing configuration
        self.binary_config = {
            "enabled": False,    # Enable binary data processing
            "mode": "mixed",     # Processing mode: "text_only", "binary_only", "mixed"
            "packet_header": 0x78,  # Binary packet header byte
            "packet_tail": 0xFC,    # Binary packet tail byte
            "min_packet_size": 7,   # Minimum binary packet size
            "max_packet_size": 32   # Maximum binary packet size
        }
    
    def set_frame_format(self, header="$$", tail="##", enabled=True):
        """Set data frame format"""
        self.frame_config["header"] = header
        self.frame_config["tail"] = tail
        self.frame_config["enabled"] = enabled

    def get_frame_config(self):
        """Get frame configuration"""
        return self.frame_config.copy()

    def set_binary_mode(self, enabled=True, mode="mixed", packet_header=0x78, packet_tail=0xFC):
        """Set binary data processing mode

        Args:
            enabled (bool): Enable binary data processing
            mode (str): Processing mode - "text_only", "binary_only", "mixed"
            packet_header (int): Binary packet header byte (0x00-0xFF)
            packet_tail (int): Binary packet tail byte (0x00-0xFF)
        """
        self.binary_config["enabled"] = enabled
        self.binary_config["mode"] = mode
        self.binary_config["packet_header"] = packet_header & 0xFF
        self.binary_config["packet_tail"] = packet_tail & 0xFF

    def get_binary_config(self):
        """Get binary processing configuration"""
        return self.binary_config.copy()

    def update_config(self, **kwargs):
        """Batch update configuration"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)

# Global configuration instance
config = Config() 