#!/usr/bin/env python3
# test_binary_uart.py - 测试二进制串口功能

from micu_uart_lib import SimpleUART

def test_binary_packet_handler(packet: bytes):
    """测试二进制数据包处理回调函数"""
    print(f"🔥 回调函数收到数据包: {packet.hex().upper()}")

def main():
    """测试主函数"""
    print("=== 测试二进制串口功能 ===")
    
    # 创建串口实例
    uart = SimpleUART()
    
    # 测试配置
    print("✅ SimpleUART 实例创建成功")
    
    # 测试二进制模式配置
    uart.set_binary_mode(
        enabled=True,
        mode="mixed",
        packet_header=0x78,
        packet_tail=0xFC
    )
    print("✅ 二进制模式配置成功")
    
    # 测试回调注册
    uart.add_binary_packet_callback(test_binary_packet_handler)
    print("✅ 回调函数注册成功")
    
    # 模拟接收数据测试
    print("\n🧪 模拟数据接收测试...")
    
    # 模拟接收文本数据
    print("📝 测试文本数据处理...")
    text_data = b"$$hello world##"
    uart._try_parse_as_text(text_data)
    
    # 模拟接收二进制数据
    print("\n🔢 测试二进制数据处理...")
    binary_data = bytes([0x78, 0x01, 0xFF, 0x50, 0x00, 0x64, 0xFC])  # 示例数据包
    
    # 直接添加到二进制缓冲区进行测试
    with uart._binary_lock:
        uart.binary_buf += binary_data
    
    # 处理数据包
    uart._process_triangle_packets()
    
    # 测试数据包接收
    packets = uart.receive_binary_packets()
    print(f"📦 接收到 {len(packets)} 个数据包")
    
    # 测试缓冲区信息
    buffer_info = uart.get_binary_buffer_info()
    print(f"📊 缓冲区状态: {buffer_info}")
    
    print("\n✅ 所有测试完成!")

if __name__ == "__main__":
    main()
