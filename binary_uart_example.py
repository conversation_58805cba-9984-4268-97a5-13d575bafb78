#!/usr/bin/env python3
# binary_uart_example.py - 二进制串口通信示例

from micu_uart_lib import SimpleUART, parse_triangle_packet, create_triangle_packet, hex_dump
from maix import time, app

def binary_packet_handler(packet: bytes):
    """二进制数据包处理回调函数"""
    print(f"\n🔥 收到二进制数据包:")
    print(f"   原始数据: {packet.hex().upper()}")
    print(f"   长度: {len(packet)} 字节")
    
    # 尝试解析为三角形数据包
    parsed = parse_triangle_packet(packet)
    if parsed:
        print(f"   📐 三角形数据包:")
        print(f"      ID: {parsed['packet_id']}")
        print(f"      坐标: ({parsed['x']}, {parsed['y']})")
    else:
        print(f"   🔢 通用二进制数据:")
        print(f"      Hex Dump:")
        for line in hex_dump(packet).split('\n'):
            print(f"         {line}")

def main():
    """主程序 - 二进制串口通信示例"""
    print("=== 二进制串口通信示例 ===")
    print("功能：演示如何处理二进制数据包")
    print("支持：三角形数据包解析、混合模式通信")
    print("=" * 40)
    
    # 初始化串口
    uart = SimpleUART()
    
    # 初始化串口连接
    if not uart.init("/dev/ttyS0", 115200, set_as_global=True):
        print("❌ 串口初始化失败")
        return
    
    print("✅ 串口初始化成功")
    
    # 配置二进制模式
    uart.set_binary_mode(
        enabled=True,           # 启用二进制处理
        mode="mixed",          # 混合模式：同时支持文本和二进制
        packet_header=0x78,    # 数据包头：0x78
        packet_tail=0xFC       # 数据包尾：0xFC
    )
    
    # 注册二进制数据包处理回调
    uart.add_binary_packet_callback(binary_packet_handler)
    
    print("\n📡 开始监听串口数据...")
    print("💡 提示：")
    print("   - 发送文本数据将按文本模式处理")
    print("   - 发送0x78开头、0xFC结尾的数据将按二进制模式处理")
    print("   - 按Ctrl+C退出")
    print("-" * 50)
    
    # 示例：发送一些测试数据包
    print("\n🚀 发送测试数据包...")
    
    # 发送三角形数据包示例
    test_packet = create_triangle_packet(packet_id=1, x=-50, y=100)
    uart.send_binary(test_packet)
    print(f"📤 发送三角形数据包: {test_packet.hex().upper()}")
    
    # 发送文本数据示例
    uart.send("$$test_message##")
    print("📤 发送文本数据: $$test_message##")
    
    # 主循环
    frame_count = 0
    last_status_time = time.ticks_ms()
    
    try:
        while not app.need_exit():
            # 检查二进制数据包
            binary_packets = uart.receive_binary_packets()
            if binary_packets:
                print(f"\n📦 本轮收到 {len(binary_packets)} 个二进制数据包")
            
            # 检查文本数据
            text_frames = uart.receive_all()
            if text_frames:
                print(f"\n📝 本轮收到 {len(text_frames)} 个文本帧:")
                for i, frame in enumerate(text_frames):
                    print(f"   帧{i+1}: '{frame}'")
            
            # 定期显示状态信息
            current_time = time.ticks_ms()
            if current_time - last_status_time > 5000:  # 每5秒
                buffer_info = uart.get_binary_buffer_info()
                print(f"\n📊 状态报告 (帧#{frame_count}):")
                print(f"   二进制缓冲区: {buffer_info['size']} 字节")
                if buffer_info['preview']:
                    print(f"   缓冲区预览: {buffer_info['preview']}")
                last_status_time = current_time
            
            frame_count += 1
            time.sleep_ms(50)  # 50ms循环间隔
            
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
    finally:
        print("🔚 程序结束")

if __name__ == "__main__":
    main()
